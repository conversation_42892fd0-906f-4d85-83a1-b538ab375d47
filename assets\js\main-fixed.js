$(document).ready(function() {
	
	function resizedR(parentsize, childsize){
		childsize.style.width = parentsize.offsetWidth;
		childsize.style.height = parentsize.offsetHeight;
		childsize.style.borderRight = parentsize.offsetWidth + 'px solid rgba(0, 0, 0, 0.35)';
		childsize.style.borderTop = parentsize.offsetHeight + 'px solid transparent';
	}
	function resizedL(parentsize, childsize){
		childsize.style.width = parentsize.offsetWidth;
		childsize.style.height = parentsize.offsetHeight;
		childsize.style.borderRight = parentsize.offsetWidth + 'px solid transparent';
		childsize.style.borderTop = parentsize.offsetHeight + 'px solid rgba(0, 0, 0, 0.35)';
	}
	window.onload = function(){
		var content = document.querySelector('.prlw');
		var triangleR = document.querySelector('.prlw-r');
		var triangleL = document.querySelector('.prlw-l');
		resizedR(content,triangleR);
		resizedL(content,triangleL);
		window.onresize = function(e){
			resizedR(content,triangleR);
			resizedL(content,triangleL);
		}
	}
	
	// شاشة التحميل - تعمل بدون PHP
	$(window).load(function(){
		$('.prlw-r').animate({
			bottom: "-=10%",
			right: "-=10%"
		}, 700);
		$('.prlw-l').animate({
			top: "-=10%",
			left: "-=10%"
		}, 700);
		setTimeout(function(){
			$('.prlc-lb div').animate({
				width: "100%"
			}, 2000);
		}, 400);
		setTimeout(function(){
			$('.prlw-r').animate({
				bottom: "-=100%",
				right: "-=100%"
			}, 700);
			$('.prlw-l').animate({
				top: "-=100%",
				left: "-=100%"
			}, 700);
			$('.prlc').animate({
				opacity: "0"
			}, 300);			
		}, 2000);
		setTimeout(function(){
			$('.prlp').animate({
				top: "-=100%",
				opacity: 0
			}, 700, function() {
				$('.prlp').remove();
			});
			aO( '.aoi', 'animate__bounceIn' );
			$('.aoi').removeClass('aoinv');
		}, 2300);
	});	
	
	// تهيئة الأصوات (اختياري)
	if (s_s == 1) {
		sp = "assets/a/";
		// تم تعطيل الأصوات لتجنب الأخطاء
		console.log("Sound system disabled for client-side version");
	}
	
	// تهيئة الجسيمات
	if($('#header-particles').length){
		// تحقق من وجود ملف التكوين
		if(typeof particlesJS !== 'undefined') {
			particlesJS('header-particles', {
				"particles": {
					"number": {
						"value": 80,
						"density": {
							"enable": true,
							"value_area": 800
						}
					},
					"color": {
						"value": "#ffffff"
					},
					"shape": {
						"type": "circle"
					},
					"opacity": {
						"value": 0.5,
						"random": false
					},
					"size": {
						"value": 3,
						"random": true
					},
					"line_linked": {
						"enable": true,
						"distance": 150,
						"color": "#ffffff",
						"opacity": 0.4,
						"width": 1
					},
					"move": {
						"enable": true,
						"speed": 6,
						"direction": "none",
						"random": false,
						"straight": false,
						"out_mode": "out",
						"bounce": false
					}
				},
				"interactivity": {
					"detect_on": "canvas",
					"events": {
						"onhover": {
							"enable": true,
							"mode": "repulse"
						},
						"onclick": {
							"enable": true,
							"mode": "push"
						},
						"resize": true
					}
				},
				"retina_detect": true
			});
		}
	}
	
	var jsp, jsp_i, peu;	
	jsp = "";	
	
	function fixplatformBox($platform_parent_class) {
        resetplatformBoxes();
        if ($platform_parent_class.hasClass('p-s-i-1')) {
            jsp = 'Windows PC';
            jsp_i = '<i class="fab fa-windows"></i>';
        }
        if ($platform_parent_class.hasClass('p-s-i-2')) {
            jsp = 'Xbox';
			jsp_i = '<i class="fab fa-xbox"></i>';
        }
		if ($platform_parent_class.hasClass('p-s-i-3')) {
            jsp = 'Playstation';
			jsp_i = '<i class="fab fa-playstation"></i>';
        }
		if ($platform_parent_class.hasClass('p-s-i-4')) {
            jsp = 'Android';
			jsp_i = '<i class="fab fa-android"></i>';
        }
		if ($platform_parent_class.hasClass('p-s-i-5')) {
            jsp = 'iOS';
			jsp_i = '<i class="fab fa-apple"></i>';
        }
        $platform_parent_class.addClass('active');
    }	
    function resetplatformBoxes() {
        var $platform_list = $('.p-s-i-1, .p-s-i-2, .p-s-i-3, .p-s-i-4, .p-s-i-5');	
        if ($platform_list.hasClass('active')) {
            $platform_list.removeClass('active');
        }
    }
	$('.p-s-i').click(function() {		
		if (!$(this).hasClass("active")) {
			aO( this, 'animate__headShake' );
		}	
		fixplatformBox($(this));
    });
	
	$c_s_m = ".prcs-m";
	$(document).on("click","#p-b-a",function() {
		if($('#u-i').val() == '') {
			aO( $('.s-e-w-u'), 'animate__flipInX' );
			$('.s-e-w-u').fadeIn(function() {					
				setTimeout(function(){
					$('.s-e-w-u').addClass('animate__animated animate__flipOutX').one('webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend', function(){
						$(this).removeClass('animate__animated animate__flipOutX');
						$(this).hide();
					});
				}, 1200);
			});
		}
		if(jsp == '') {
			aO( $('.s-e-w-p'), 'animate__flipInX' );
			$('.s-e-w-p').fadeIn(function() {					
				setTimeout(function(){
					$('.s-e-w-p').addClass('animate__animated animate__flipOutX').one('webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend', function(){
						$(this).removeClass('animate__animated animate__flipOutX');
						$(this).hide();
					});
				}, 1200);
			});
		}
		if(jsp != '' && $('#u-i').val() != '') {
			peu = $('#u-i').val();
			showSuccessMessage();
		}
	});	
	
	$('#u-i').on('keypress', function (e) {
         if(e.which === 13){
            $(this).attr("disabled", "disabled");
			if($('#u-i').val() == '') {
				aO( $('.s-e-w-u'), 'animate__flipInX' );
				$('.s-e-w-u').fadeIn(function() {			
					setTimeout(function(){
						$('.s-e-w-u').addClass('animate__animated animate__flipOutX').one('webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend', function(){
							$(this).removeClass('animate__animated animate__flipOutX');
							$(this).hide();
						});
					}, 1200);
				});
				$('#u-i').removeAttr("disabled");
			}
			if(jsp == '') {
				aO( $('.s-e-w-p'), 'animate__flipInX' );
				$('.s-e-w-p').fadeIn(function() {					
					setTimeout(function(){
						$('.s-e-w-p').addClass('animate__animated animate__flipOutX').one('webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend', function(){
							$(this).removeClass('animate__animated animate__flipOutX');
							$(this).hide();
						});
					}, 1200);
				});
				$('#u-i').removeAttr("disabled");
			}
			if(jsp != '' && $('#u-i').val() != '') {
				peu = $('#u-i').val();
				showSuccessMessage();
			}
         }
   });
	
	// دالة لإظهار رسالة النجاح بدلاً من تحميل PHP
	function showSuccessMessage() {
		// إنشاء محتوى HTML للنجاح
		var successHTML = `
			<div class="c-w c-w-success aoi aoinv">
				<div class="s-o-w">
					<div class="s-o aoi aoinv animation-delay-100">
						<span>2</span>
					</div>
				</div>
				<div class="success-content">
					<div class="c-i-t aoi aoinv animation-delay-200">
						<span class="c-i-t-v">تم التحقق من البيانات بنجاح!</span>
					</div>
					<div class="success-details">
						<div class="user-info">
							<h3>معلومات المستخدم:</h3>
							<p><strong>اسم المستخدم:</strong> ${peu}</p>
							<p><strong>المنصة:</strong> ${jsp}</p>
						</div>
						<div class="success-message">
							<div class="success-icon">
								<svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
									<circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none"/>
									<path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8"/>
								</svg>
							</div>
							<h2>تم بنجاح!</h2>
							<p>تم التحقق من بياناتك وتجهيز الموارد.</p>
							<button id="restart-btn" class="p-b">ابدأ من جديد</button>
						</div>
					</div>
				</div>
			</div>
		`;
		
		// إضافة CSS للنجاح
		if (!$('#success-styles').length) {
			$('head').append(`
				<style id="success-styles">
					.c-w-success {
						background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
						border-radius: 15px;
						padding: 30px;
						color: white;
						text-align: center;
					}
					.success-details {
						margin-top: 20px;
					}
					.user-info {
						background: rgba(255,255,255,0.1);
						padding: 20px;
						border-radius: 10px;
						margin-bottom: 20px;
					}
					.success-message {
						background: rgba(255,255,255,0.1);
						padding: 30px;
						border-radius: 10px;
					}
					.success-icon {
						margin-bottom: 20px;
					}
					.checkmark {
						width: 80px;
						height: 80px;
						border-radius: 50%;
						display: block;
						stroke-width: 2;
						stroke: #4CAF50;
						stroke-miterlimit: 10;
						margin: 0 auto;
						box-shadow: inset 0px 0px 0px #4CAF50;
						animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
					}
					.checkmark__circle {
						stroke-dasharray: 166;
						stroke-dashoffset: 166;
						stroke-width: 2;
						stroke-miterlimit: 10;
						stroke: #4CAF50;
						fill: none;
						animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
					}
					.checkmark__check {
						transform-origin: 50% 50%;
						stroke-dasharray: 48;
						stroke-dashoffset: 48;
						animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
					}
					@keyframes stroke {
						100% {
							stroke-dashoffset: 0;
						}
					}
					@keyframes scale {
						0%, 100% {
							transform: none;
						}
						50% {
							transform: scale3d(1.1, 1.1, 1);
						}
					}
					@keyframes fill {
						100% {
							box-shadow: inset 0px 0px 0px 30px #4CAF50;
						}
					}
					#restart-btn {
						margin-top: 20px;
						background: #4CAF50;
						border: none;
						color: white;
						padding: 15px 30px;
						border-radius: 25px;
						cursor: pointer;
						font-size: 16px;
						transition: all 0.3s ease;
					}
					#restart-btn:hover {
						background: #45a049;
						transform: translateY(-2px);
					}
				</style>
			`);
		}
		
		// تحديث المحتوى
		$( ".b-s-c-w" ).addClass('animate__animated animate__bounceOut');
		setTimeout(function(){			
			$( '.b-s-c-w' ).hide();
			$( '.a-s-c-w' ).html(successHTML).hide().fadeIn();	
			aO( '.c-w-success', 'animate__bounceIn' );
		}, 600);	
	}
	
	// إعادة تشغيل التطبيق
	$(document).on("click","#restart-btn",function() {
		location.reload();
	});
	
	function aO(el, anim, onDone) {
		var $el = $(el);
		$el.addClass( 'animate__animated ' + anim );
		$el.one( 'animationend', function() {
			$(this).removeClass( 'animate__animated ' + anim );
			onDone && onDone();
		});
	}
});
