$(document).ready(function() {
	
	function resizedR(parentsize, childsize){
		childsize.style.width = parentsize.offsetWidth;
		childsize.style.height = parentsize.offsetHeight;
		childsize.style.borderRight = parentsize.offsetWidth + 'px solid rgba(0, 0, 0, 0.35)';
		childsize.style.borderTop = parentsize.offsetHeight + 'px solid transparent';
	}
	function resizedL(parentsize, childsize){
		childsize.style.width = parentsize.offsetWidth;
		childsize.style.height = parentsize.offsetHeight;
		childsize.style.borderRight = parentsize.offsetWidth + 'px solid transparent';
		childsize.style.borderTop = parentsize.offsetHeight + 'px solid rgba(0, 0, 0, 0.35)';
	}
	window.onload = function(){
		var content = document.querySelector('.prlw');
		var triangleR = document.querySelector('.prlw-r');
		var triangleL = document.querySelector('.prlw-l');
		resizedR(content,triangleR);
		resizedL(content,triangleL);
		window.onresize = function(e){
			resizedR(content,triangleR);
			resizedL(content,triangleL);
		}
	}
	
	// شاشة التحميل - تعمل بدون PHP
	$(window).load(function(){
		$('.prlw-r').animate({
			bottom: "-=10%",
			right: "-=10%"
		}, 700);
		$('.prlw-l').animate({
			top: "-=10%",
			left: "-=10%"
		}, 700);
		setTimeout(function(){
			$('.prlc-lb div').animate({
				width: "100%"
			}, 2000);
		}, 400);
		setTimeout(function(){
			$('.prlw-r').animate({
				bottom: "-=100%",
				right: "-=100%"
			}, 700);
			$('.prlw-l').animate({
				top: "-=100%",
				left: "-=100%"
			}, 700);
			$('.prlc').animate({
				opacity: "0"
			}, 300);			
		}, 2000);
		setTimeout(function(){
			$('.prlp').animate({
				top: "-=100%",
				opacity: 0
			}, 700, function() {
				$('.prlp').remove();
			});
			aO( '.aoi', 'animate__bounceIn' );
			$('.aoi').removeClass('aoinv');
		}, 2300);
	});	
	
	// تهيئة الأصوات (اختياري)
	if (s_s == 1) {
		sp = "assets/a/";
		// تم تعطيل الأصوات لتجنب الأخطاء
		console.log("Sound system disabled for client-side version");
	}
	
	// تهيئة الجسيمات
	if($('#header-particles').length){
		// تحقق من وجود ملف التكوين
		if(typeof particlesJS !== 'undefined') {
			particlesJS('header-particles', {
				"particles": {
					"number": {
						"value": 80,
						"density": {
							"enable": true,
							"value_area": 800
						}
					},
					"color": {
						"value": "#ffffff"
					},
					"shape": {
						"type": "circle"
					},
					"opacity": {
						"value": 0.5,
						"random": false
					},
					"size": {
						"value": 3,
						"random": true
					},
					"line_linked": {
						"enable": true,
						"distance": 150,
						"color": "#ffffff",
						"opacity": 0.4,
						"width": 1
					},
					"move": {
						"enable": true,
						"speed": 6,
						"direction": "none",
						"random": false,
						"straight": false,
						"out_mode": "out",
						"bounce": false
					}
				},
				"interactivity": {
					"detect_on": "canvas",
					"events": {
						"onhover": {
							"enable": true,
							"mode": "repulse"
						},
						"onclick": {
							"enable": true,
							"mode": "push"
						},
						"resize": true
					}
				},
				"retina_detect": true
			});
		}
	}
	
	var jsp, jsp_i, peu;	
	jsp = "";	
	
	function fixplatformBox($platform_parent_class) {
        resetplatformBoxes();
        if ($platform_parent_class.hasClass('p-s-i-1')) {
            jsp = 'Windows PC';
            jsp_i = '<i class="fab fa-windows"></i>';
        }
        if ($platform_parent_class.hasClass('p-s-i-2')) {
            jsp = 'Xbox';
			jsp_i = '<i class="fab fa-xbox"></i>';
        }
		if ($platform_parent_class.hasClass('p-s-i-3')) {
            jsp = 'Playstation';
			jsp_i = '<i class="fab fa-playstation"></i>';
        }
		if ($platform_parent_class.hasClass('p-s-i-4')) {
            jsp = 'Android';
			jsp_i = '<i class="fab fa-android"></i>';
        }
		if ($platform_parent_class.hasClass('p-s-i-5')) {
            jsp = 'iOS';
			jsp_i = '<i class="fab fa-apple"></i>';
        }
        $platform_parent_class.addClass('active');
    }	
    function resetplatformBoxes() {
        var $platform_list = $('.p-s-i-1, .p-s-i-2, .p-s-i-3, .p-s-i-4, .p-s-i-5');	
        if ($platform_list.hasClass('active')) {
            $platform_list.removeClass('active');
        }
    }
	$('.p-s-i').click(function() {		
		if (!$(this).hasClass("active")) {
			aO( this, 'animate__headShake' );
		}	
		fixplatformBox($(this));
    });
	
	$c_s_m = ".prcs-m";
	$(document).on("click","#p-b-a",function() {
		if($('#u-i').val() == '') {
			aO( $('.s-e-w-u'), 'animate__flipInX' );
			$('.s-e-w-u').fadeIn(function() {					
				setTimeout(function(){
					$('.s-e-w-u').addClass('animate__animated animate__flipOutX').one('webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend', function(){
						$(this).removeClass('animate__animated animate__flipOutX');
						$(this).hide();
					});
				}, 1200);
			});
		}
		if(jsp == '') {
			aO( $('.s-e-w-p'), 'animate__flipInX' );
			$('.s-e-w-p').fadeIn(function() {					
				setTimeout(function(){
					$('.s-e-w-p').addClass('animate__animated animate__flipOutX').one('webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend', function(){
						$(this).removeClass('animate__animated animate__flipOutX');
						$(this).hide();
					});
				}, 1200);
			});
		}
		if(jsp != '' && $('#u-i').val() != '') {
			peu = $('#u-i').val();
			showSuccessMessage();
		}
	});	
	
	$('#u-i').on('keypress', function (e) {
         if(e.which === 13){
            $(this).attr("disabled", "disabled");
			if($('#u-i').val() == '') {
				aO( $('.s-e-w-u'), 'animate__flipInX' );
				$('.s-e-w-u').fadeIn(function() {			
					setTimeout(function(){
						$('.s-e-w-u').addClass('animate__animated animate__flipOutX').one('webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend', function(){
							$(this).removeClass('animate__animated animate__flipOutX');
							$(this).hide();
						});
					}, 1200);
				});
				$('#u-i').removeAttr("disabled");
			}
			if(jsp == '') {
				aO( $('.s-e-w-p'), 'animate__flipInX' );
				$('.s-e-w-p').fadeIn(function() {					
					setTimeout(function(){
						$('.s-e-w-p').addClass('animate__animated animate__flipOutX').one('webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend', function(){
							$(this).removeClass('animate__animated animate__flipOutX');
							$(this).hide();
						});
					}, 1200);
				});
				$('#u-i').removeAttr("disabled");
			}
			if(jsp != '' && $('#u-i').val() != '') {
				peu = $('#u-i').val();
				showSuccessMessage();
			}
         }
   });
	
	// دالة لإظهار مرحلة البحث الأولى (من search1.html)
	function showSuccessMessage() {
		// استخدام نفس HTML الموجود في search1.html
		var searchHTML = `
			<div class="s-f-p-w">
				<div class="s-f-p-i">
					<div class="s-f-p-t-w">
						<div class="s-f-p-t-l animate__animated animate__bounceIn"><div class="animate__animated animate__pulse animate__infinite">Searching...</div></div>
						<div class="caSs">
							<div class="caSsi">
								<div class="animate__animated animate__bounceIn animation-delay-100">
									<div class="s-f-p-t-u-w animate__animated animate__pulse animate__infinite"><div id="s-f-p-t-u-v" class="s-f-p-t-u-v">${peu}</div></div>
								</div>
								<div class="animate__animated animate__bounceIn animation-delay-200">
									<div class="s-f-p-t-p-w animate__animated animate__pulse animate__infinite"><div id="s-f-p-t-p-v" class="s-f-p-t-p-v">${jsp_i}</div></div>
								</div>
							</div>
						</div>
					</div>
					<div class="s-f-p-a-w animate__animated animate__bounceIn animation-delay-400">
						<img src="assets/img/mgi.png" class="mgi img-fluid ssa" style="width: 125px; height: 125px;">
					</div>
				</div>
			</div>
		`;

		// تحديث المحتوى لإظهار مرحلة البحث
		$( ".c-w" ).addClass('animate__animated animate__bounceOut');
		setTimeout(function(){
			$( '.m-s .container' ).html(searchHTML).hide().fadeIn();
			aO( '.s-f-p-w', 'animate__bounceIn' );

			// الانتقال لمرحلة اختيار الجواهر بعد 3 ثواني
			setTimeout(function() {
				showGemsSelection();
			}, 3000);
		}, 600);
	}

	// دالة لإظهار مرحلة اختيار كمية الجواهر (المرحلة 2)
	function showGemsSelection() {
		// استخدام نفس HTML الموجود في step2.html
		var gemsSelectionHTML = `
			<div class="acpeou">
				<div class="c-w c-w-r">
					<div class="s-o-w">
						<div class="s-o">
							<span>2</span>
						</div>
					</div>
					<div class="c-i-t">
						<span class="c-i-t-v">Please select the amount of Gems.</span>
					</div>
					<div class="r-i-s-o-w">
						<div class="r-i-s-i-w">
							<div class="r-i-s-w">
								<div class="r-i-s-r-w animate__animated animate__fadeIn animation-delay-200">
									<div class="r-i-s-r-h-w"><span>Selected Amount</span></div>
									<img src="assets/img/1542376423e620a935bc336f7423ab385e8fccba52.png" class="img-fluid r-i-s-r-w-i">
									<div class="r-i-s-r-m-w">
										<div class="r-i-s-r-m-v">2500</div>
										<div class="r-i-s-r-m-l">Gems</div>
									</div>
								</div>
							</div>
						</div>
						<div class="r-s-i-w">
							<div class="r-s-i r-s-i-1 active animation-delay-200">
								<img src="assets/img/1542376423e620a935bc336f7423ab385e8fccba52.png" class="img-fluid r-s-i-i">
								<div class="r-s-i-v">2500</div>
								<div class="r-s-i-l">Gems</div>
							</div>
							<div class="r-s-i r-s-i-2 animation-delay-400">
								<img src="assets/img/1542376423e620a935bc336f7423ab385e8fccba52.png" class="img-fluid r-s-i-i">
								<div class="r-s-i-v">5000</div>
								<div class="r-s-i-l">Gems</div>
							</div>
							<div class="r-s-i r-s-i-3 animation-delay-600">
								<img src="assets/img/1542376423e620a935bc336f7423ab385e8fccba52.png" class="img-fluid r-s-i-i">
								<div class="r-s-i-v">7500</div>
								<div class="r-s-i-l">Gems</div>
							</div>
							<div class="r-s-i r-s-i-4 animation-delay-800">
								<img src="assets/img/1542376423e620a935bc336f7423ab385e8fccba52.png" class="img-fluid r-s-i-i">
								<div class="r-s-i-v">10000</div>
								<div class="r-s-i-l">Gems</div>
							</div>
						</div>
					</div>
					<div class="p-b-w animate__animated animate__bounceIn animation-delay-1000">
						<div id="p-b-r" class="p-b"><span>Proceed</span></div>
					</div>
				</div>
			</div>
		`;

		// تحديث المحتوى لإظهار مرحلة اختيار الجواهر
		$( ".s-f-p-w" ).addClass('animate__animated animate__bounceOut');
		setTimeout(function(){
			$( '.m-s .container' ).html(gemsSelectionHTML).hide().fadeIn();
			aO( '.c-w-r', 'animate__bounceIn' );

			// تفعيل وظائف اختيار الجواهر
			initGemsSelection();
		}, 600);
	}

	// دالة لتفعيل وظائف اختيار الجواهر
	function initGemsSelection() {
		// متغيرات الجواهر
		var selectedGems = "2500";

		// وظيفة اختيار كمية الجواهر
		function fixGemsBox($gems_parent_class) {
			resetGemsBoxes();
			if ($gems_parent_class.hasClass('r-s-i-1')) {
				selectedGems = '2500';
			}
			if ($gems_parent_class.hasClass('r-s-i-2')) {
				selectedGems = '5000';
			}
			if ($gems_parent_class.hasClass('r-s-i-3')) {
				selectedGems = '7500';
			}
			if ($gems_parent_class.hasClass('r-s-i-4')) {
				selectedGems = '10000';
			}
			$gems_parent_class.addClass('active');

			// تحديث العرض
			$('.r-i-s-r-m-v').text(selectedGems);
		}

		function resetGemsBoxes() {
			var $gems_list = $('.r-s-i-1, .r-s-i-2, .r-s-i-3, .r-s-i-4');
			if ($gems_list.hasClass('active')) {
				$gems_list.removeClass('active');
			}
		}

		// معالج النقر على خيارات الجواهر
		$('.r-s-i').click(function() {
			$('.r-s-i').removeClass('animation-delay-200 animation-delay-400 animation-delay-600 animation-delay-800');
			if (!$(this).hasClass("active")) {
				aO( this, 'animate__headShake' );
				aO( '.r-i-s-r-m-v', 'animate__flipInX' );
			}
			fixGemsBox($(this));
		});

		// معالج زر المتابعة لإظهار مرحلة البحث الثانية
		$(document).on("click","#p-b-r",function() {
			showSearch2Screen(selectedGems);
		});
	}

	// دالة لإظهار مرحلة البحث الثانية (من search2.html)
	function showSearch2Screen(gems) {
		// استخدام نفس HTML الموجود في search2.html
		var search2HTML = `
			<div class="c-w c-w-p" style="top: -96px;">
				<div class="s-o-w">
					<div class="s-o">
						<span>3</span>
					</div>
				</div>
				<div class="p-i-w">
					<div class="prcs-l"><span class="material-icons-two-tone fa-spin">settings</span></div>
					<div class="prcs-m">Syncing ${gems} Gems for ${peu}...</div>
					<div id="pBC" class="p-lb"><div style="width: 107.2px;">40%&nbsp;</div></div>
				</div>
			</div>
			<div class="c-w-p-g-i-c">
				<div class="c-w-p-g-i" style="bottom: -96px; opacity: 1;">
					<div class="c-w-p-g-i-i">
						<div class="c-w-p-g-i-i-c-w">
							<span class="material-icons-two-tone fa-spin">settings</span>
						</div>
						<div class="c-w-p-g-i-i-t">
							<img src="assets/img/1542376423e620a935bc336f7423ab385e8fccba52.png" class="img-fluid c-w-p-g-i-i-t-i">
						</div>
						<div class="c-w-p-g-i-i-b">
							<div id="c-w-p-g-i-i-b-v" class="c-w-p-g-i-i-b-v">${gems}</div>
							<div class="c-w-p-g-i-i-b-l">Gems</div>
						</div>
						<div class="cwPBw">
							<div id="cwPB" class="p-lb"><div style="width: 31.8562px; overflow: hidden;">100%&nbsp;</div></div>
						</div>
					</div>
				</div>
			</div>
		`;

		// تحديث المحتوى لإظهار مرحلة البحث الثانية
		$( ".acpeou" ).addClass('animate__animated animate__bounceOut');
		setTimeout(function(){
			$( '.m-s .container' ).html(search2HTML).hide().fadeIn();
			aO( '.c-w-p', 'animate__bounceIn' );

			// الانتقال للمرحلة الثالثة بعد 4 ثواني
			setTimeout(function() {
				showProcessingScreen(gems);
			}, 4000);
		}, 600);
	}

	// دالة لإظهار المرحلة الثالثة (من step3.html)
	function showProcessingScreen(gems) {
		// استخدام نفس HTML الموجود في step3.html
		var step3HTML = `
			<div class="c-w c-w-l animate__animated animate__bounceIn">
				<div class="s-o-w">
					<div class="s-o">
						<span class="material-icons-two-tone fa-spin">rotate_right</span>
					</div>
				</div>
				<div class="c-i-t">
					<h4 class="c-w-l-t-v animation-delay-100">Last Step</h4>
					<div class="c-w-l-p-v animation-delay-200">Hello <span class="lsv2s">${peu}</span>! You are almost done with synchronization of <span class="lsv2s">${gems}</span> <span class="lsv2s">Gems</span>! Please complete the last step by clicking the button below to finish with synchronization process.</div>
				</div>
				<div class="c-w-l-r-o-w animation-delay-300">
					<div class="c-w-p-g-i-i animate__animated animate__pulse animate__infinite">
						<div class="c-w-p-g-i-i-c-w c-w-p-g-i-i-c-w-s"><span class="material-icons-two-tone mitt-r">check_circle</span></div>
						<div class="c-w-p-g-i-i-t"><svg class="caSs-cm caSs-cm-s" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52"><circle class="caSs-cm-ci" cx="26" cy="26" r="25" fill="none"></circle><path class="caSs-cm-ch" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8"></path></svg></div>
						<div class="c-w-p-g-i-i-b">
							<div id="c-w-p-g-i-i-b-v" class="c-w-p-g-i-i-b-v">${gems}</div>
							<div class="c-w-p-g-i-i-b-l">Gems</div>
						</div>
						<div class="cwPBw cwPBws">
							<div id="cwPB" class="p-lb"><div style="width: 150px;">100%&nbsp;</div></div>
						</div>
					</div>
				</div>
				<div class="p-b-w p-b-l-s-w animation-delay-400">
					<a id="l-s-v-b" class="p-b"><span>Verify Now</span></a>
				</div>
			</div>
		`;

		$( ".acpeou" ).addClass('animate__animated animate__bounceOut');
		setTimeout(function(){
			$( '.m-s .container' ).html(step3HTML).hide().fadeIn();
			aO( '.c-w-l', 'animate__bounceIn' );

			// معالج زر التحقق النهائي
			$(document).on("click","#l-s-v-b",function() {
				showFinalSuccess(gems);
			});
		}, 600);
	}

	// إضافة CSS للشاشات الجديدة
	if (!$('#search-styles').length) {
		$('head').append(`
			<style id="search-styles">
				/* تحديد حجم الصورة في مراحل البحث */
				.mgi.img-fluid.ssa {
					width: 125px !important;
					height: 125px !important;
					max-width: 125px !important;
					max-height: 125px !important;
				}

				/* الشاشة النهائية */
				.c-w-final {
					background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
					border-radius: 15px;
					padding: 40px;
					color: white;
					text-align: center;
					max-width: 500px;
					margin: 0 auto;
				}

				.final-content h2 {
					font-size: 2.5em;
					margin-bottom: 20px;
					color: #fff;
					font-weight: bold;
				}

				.final-content p {
					font-size: 1.2em;
					margin-bottom: 20px;
				}

				.final-details {
					background: rgba(255,255,255,0.1);
					padding: 20px;
					border-radius: 10px;
					margin: 20px 0;
				}

				.final-details p {
					margin: 8px 0;
					font-size: 1.1em;
				}

				#restart-btn {
					margin-top: 20px;
					background: #fff;
					border: none;
					color: #4CAF50;
					padding: 15px 30px;
					border-radius: 25px;
					cursor: pointer;
					font-size: 16px;
					font-weight: bold;
					transition: all 0.3s ease;
					box-shadow: 0 4px 15px rgba(0,0,0,0.2);
				}

				#restart-btn:hover {
					background: #f0f0f0;
					transform: translateY(-2px);
					box-shadow: 0 6px 20px rgba(0,0,0,0.3);
				}

				/* تصميم متجاوب */
				@media (max-width: 768px) {
					.c-w-final {
						padding: 20px;
					}

					.final-content h2 {
						font-size: 2em;
					}

					.mgi.img-fluid.ssa {
						width: 100px !important;
						height: 100px !important;
						max-width: 100px !important;
						max-height: 100px !important;
					}
				}
			</style>
		`);
	}

	// تم نقل معالج الأحداث إلى داخل الدوال المناسبة

	// دالة لإظهار النجاح النهائي
	function showFinalSuccess(gems) {
		var finalHTML = `
			<div class="c-w c-w-final aoi aoinv">
				<div class="s-o-w">
					<div class="s-o aoi aoinv animation-delay-100">
						<span>✓</span>
					</div>
				</div>
				<div class="final-content">
					<h2>🎉 Success!</h2>
					<p>Your ${gems} gems have been generated successfully!</p>
					<div class="final-details">
						<p><strong>Username:</strong> ${peu}</p>
						<p><strong>Platform:</strong> ${jsp}</p>
						<p><strong>Generated:</strong> ${gems} Gems</p>
						<p><strong>Status:</strong> Ready to use</p>
					</div>
					<button id="restart-btn" class="p-b">Start Over</button>
				</div>
			</div>
		`;

		$( ".c-w-l" ).addClass('animate__animated animate__bounceOut');
		setTimeout(function(){
			$( '.m-s .container' ).html(finalHTML).hide().fadeIn();
			aO( '.c-w-final', 'animate__bounceIn' );
		}, 600);
	}

	// إعادة تشغيل التطبيق
	$(document).on("click","#restart-btn",function() {
		location.reload();
	});
	
	function aO(el, anim, onDone) {
		var $el = $(el);
		$el.addClass( 'animate__animated ' + anim );
		$el.one( 'animationend', function() {
			$(this).removeClass( 'animate__animated ' + anim );
			onDone && onDone();
		});
	}
});
